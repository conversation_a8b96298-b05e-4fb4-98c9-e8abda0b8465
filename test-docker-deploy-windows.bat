@echo off
echo ======================================
echo Windows Docker 本地测试部署
echo ======================================

REM 检查Docker是否安装
echo 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Docker，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到docker-compose
    echo 请确保Docker Desktop已正确安装
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过
echo Docker版本: 
docker --version
echo Docker Compose版本:
docker-compose --version
echo.

REM 创建测试目录
echo 创建测试环境...
if exist docker-test rmdir /s /q docker-test
mkdir docker-test
cd docker-test

REM 复制Docker配置文件
echo 复制配置文件...
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul

REM 复制服务器文件
echo 复制服务器文件...
xcopy ..\server server\ /E /I /Q
if errorlevel 1 (
    echo ❌ 错误：复制server目录失败
    pause
    exit /b 1
)

REM 复制前端文件
echo 复制前端文件...
xcopy ..\web web\ /E /I /Q
if errorlevel 1 (
    echo ❌ 错误：复制web目录失败
    pause
    exit /b 1
)

REM 创建测试环境配置
echo 创建测试配置...
(
echo # AI智能助手 - Windows测试环境配置
echo NODE_ENV=production
echo PORT=3001
echo BIND_HOST=0.0.0.0
echo.
echo # 安全配置
echo JWT_SECRET=test_jwt_secret_for_windows_testing_only
echo ADMIN_EMAIL=<EMAIL>
echo ADMIN_PASSWORD=test123456
echo.
echo # 数据库配置
echo DATABASE_PATH=./server/data/app.db
echo.
echo # CORS配置 - 允许本地访问
echo CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001
echo.
echo # 功能开关
echo ENABLE_REGISTRATION=true
echo REQUIRE_ADMIN_APPROVAL=true
echo ENABLE_CHAT_HISTORY=true
) > server\.env

REM 创建数据目录
echo 创建数据目录...
mkdir server\data 2>nul
mkdir server\logs 2>nul

echo.
echo ======================================
echo 开始Docker构建和部署...
echo ======================================

REM 停止可能存在的容器
echo 清理现有容器...
docker-compose down 2>nul

REM 构建并启动
echo 构建Docker镜像...
docker-compose build
if errorlevel 1 (
    echo ❌ Docker构建失败
    pause
    exit /b 1
)

echo 启动容器...
docker-compose up -d
if errorlevel 1 (
    echo ❌ 容器启动失败
    pause
    exit /b 1
)

echo.
echo ======================================
echo 等待服务启动...
echo ======================================

REM 等待服务启动
timeout /t 10 /nobreak >nul

REM 检查容器状态
echo 检查容器状态...
docker-compose ps

echo.
echo ======================================
echo 测试服务连接...
echo ======================================

REM 测试健康检查
echo 测试健康检查端点...
curl -s http://localhost:3001/healthz
if errorlevel 1 (
    echo.
    echo ⚠️ 健康检查失败，查看容器日志...
    docker-compose logs ai-assistant
) else (
    echo.
    echo ✅ 健康检查通过！
)

echo.
echo ======================================
echo 部署完成！
echo ======================================
echo.
echo 🌐 访问地址:
echo   主页: http://localhost:3001
echo   管理后台: http://localhost:3001/admin-new.html
echo   健康检查: http://localhost:3001/healthz
echo.
echo 👤 测试账户:
echo   邮箱: <EMAIL>
echo   密码: test123456
echo.
echo 🔧 管理命令:
echo   查看状态: docker-compose ps
echo   查看日志: docker-compose logs -f ai-assistant
echo   重启服务: docker-compose restart
echo   停止服务: docker-compose down
echo.
echo ⚠️ 这是测试环境，请勿用于生产！
echo ======================================

REM 询问是否打开浏览器
echo.
set /p open_browser="是否打开浏览器测试? (y/n): "
if /i "%open_browser%"=="y" (
    echo 正在打开浏览器...
    start http://localhost:3001
)

echo.
echo 测试完成后，运行以下命令清理:
echo   cd docker-test
echo   docker-compose down
echo   cd ..
echo   rmdir /s /q docker-test
echo.
pause
