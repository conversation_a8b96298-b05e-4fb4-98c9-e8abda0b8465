@echo off
echo ======================================
echo Docker 快速测试
echo ======================================

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 请先安装Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker已安装

REM 创建临时测试目录
if exist temp-test rmdir /s /q temp-test
mkdir temp-test
cd temp-test

REM 复制必要文件
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul
xcopy ..\server server\ /E /I /Q >nul
xcopy ..\web web\ /E /I /Q >nul

REM 创建简单配置
echo NODE_ENV=production > server\.env
echo PORT=3001 >> server\.env
echo BIND_HOST=0.0.0.0 >> server\.env
echo JWT_SECRET=test123 >> server\.env
echo ADMIN_EMAIL=<EMAIL> >> server\.env
echo ADMIN_PASSWORD=test123 >> server\.env
echo DATABASE_PATH=./server/data/app.db >> server\.env

mkdir server\data 2>nul
mkdir server\logs 2>nul

echo 构建并启动...
docker-compose down 2>nul
docker-compose up --build -d

echo 等待启动...
timeout /t 15 /nobreak >nul

echo 测试连接...
curl -s http://localhost:3001/healthz

echo.
echo ======================================
echo 测试结果:
docker-compose ps
echo ======================================
echo.
echo 访问: http://localhost:3001
echo 账户: <EMAIL> / test123
echo.
echo 清理命令: 
echo   docker-compose down
echo   cd .. ^&^& rmdir /s /q temp-test
echo.
pause
